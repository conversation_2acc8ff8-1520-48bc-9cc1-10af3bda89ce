// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get appMotto => 'El diario de tu proyecto';

  @override
  String get yes => 'Si';

  @override
  String get no => 'No';

  @override
  String get monday => 'Lunes';

  @override
  String get tuesday => 'Martes';

  @override
  String get wednesday => 'Miércoles';

  @override
  String get thursday => 'Jueves';

  @override
  String get friday => 'Viernes';

  @override
  String get saturday => 'Sabado';

  @override
  String get sunday => 'Domingo';

  @override
  String get days => 'días';

  @override
  String get lastWeek => 'La semana pasada';

  @override
  String get yesterday => 'Ayer';

  @override
  String get anHourAgo => 'Hace una hora';

  @override
  String get aMinuteAgo => 'Hace un minuto';

  @override
  String get justNow => 'Hace un momento';

  @override
  String get anHourLeft => 'Queda 1h';

  @override
  String get aMinuteLeft => 'Queda 1m';

  @override
  String get email => 'Email';

  @override
  String get emails => 'Emails';

  @override
  String get password => 'Contraseña';

  @override
  String get login => 'Iniciar sesión';

  @override
  String get logout => 'Cerrar sesión';

  @override
  String get signup => 'Crear cuenta';

  @override
  String get reset => 'Restablecer';

  @override
  String get back => 'Atrás';

  @override
  String get cancel => 'Cancelar';

  @override
  String get grant => 'Permitir';

  @override
  String get edit => 'Editar';

  @override
  String get finish => 'Terminar';

  @override
  String get update => 'Actualizar';

  @override
  String get save => 'Guardar';

  @override
  String get delete => 'Eliminar';

  @override
  String get type => 'Tipo';

  @override
  String get date => 'Fecha';

  @override
  String get birthday => 'Fecha de nacimiento';

  @override
  String get status => 'Status';

  @override
  String get progress => 'Progreso';

  @override
  String get completed => 'Completado';

  @override
  String get failed => 'Fallido';

  @override
  String get download => 'Descargar';

  @override
  String get open => 'Abrir';

  @override
  String get openWith => 'Abrir con';

  @override
  String get tag => 'Etiqueta';

  @override
  String get tags => 'Etiquetas';

  @override
  String get sync => 'Sincronizar';

  @override
  String get syncing => 'Sincronizando';

  @override
  String get welcome => 'Bienvenido';

  @override
  String get address => 'Dirección';

  @override
  String get addresses => 'Direcciones';

  @override
  String get latitude => 'Latitud';

  @override
  String get longitude => 'Longitud';

  @override
  String get send => 'Enviar';

  @override
  String get sending => 'Enviando';

  @override
  String get sent => 'Enviado';

  @override
  String get share => 'Compartir';

  @override
  String get sharing => 'Compartiendo';

  @override
  String get staff => 'Miembros';

  @override
  String get simple => 'Simple';

  @override
  String get switch_ => 'Cambiar';

  @override
  String get invite => 'Invitar';

  @override
  String get inProgress => 'En progreso...';

  @override
  String get resend => 'Reenviar';

  @override
  String get deactivate => 'Desactivar';

  @override
  String get audioRecording => 'Grabación de audio';

  @override
  String get voiceRecognition => 'Reconocimiento de voz';

  @override
  String get speechNote => 'Nota de voz';

  @override
  String get trackerEntry => 'Registro rastreado';

  @override
  String get other => 'Otros';

  @override
  String get otherSingular => 'Otro';

  @override
  String get feed => 'Feed';

  @override
  String get posts => 'Publicaciones';

  @override
  String get resources => 'Recursos';

  @override
  String get readMore => 'Leer más';

  @override
  String get report => 'Reporte';

  @override
  String get reports => 'Reportes';

  @override
  String get spreadsheet => 'Tabla';

  @override
  String get creating => 'Creando';

  @override
  String get processing => 'Procesando';

  @override
  String get opening => 'Abriendo';

  @override
  String get today => 'Hoy';

  @override
  String get elapsed => 'Transcurrido';

  @override
  String get start => 'Iniciar';

  @override
  String get stop => 'Detener';

  @override
  String get notifications => 'Notificaciones';

  @override
  String get title => 'Título';

  @override
  String get subtitle => 'Subtítulo';

  @override
  String get text => 'Texto';

  @override
  String get view => 'Ver';

  @override
  String get all => 'Todos';

  @override
  String get allWithTemplates => 'Todos (con plantillas)';

  @override
  String get clear => 'Limpiar';

  @override
  String get clearAll => 'Limpiar todo';

  @override
  String get assignee => 'Asignado';

  @override
  String get signee => 'Firmado';

  @override
  String get creator => 'Creador';

  @override
  String get client => 'Cliente';

  @override
  String get include => 'Incluir';

  @override
  String get customize => 'Customizar';

  @override
  String get template => 'Plantilla';

  @override
  String get retry => 'Reintentar';

  @override
  String get retrieve => 'Recuperar';

  @override
  String get project => 'Proyecto';

  @override
  String get company => 'Compañía';

  @override
  String get area => 'Área';

  @override
  String get approved => 'Aprobado';

  @override
  String get rejected => 'Rechazado';

  @override
  String get received => 'Recibido';

  @override
  String get new_ => 'Nuevo';

  @override
  String get create => 'Crear';

  @override
  String get missing => 'Perdido';

  @override
  String get empty => 'Vacío';

  @override
  String get tryAgain => 'Volver a intentar';

  @override
  String get longPress => 'Mantén Presionado';

  @override
  String get discard => 'Descartar';

  @override
  String get entries => 'Registros';

  @override
  String get home => 'Casa';

  @override
  String get office => 'Oficina';

  @override
  String get work => 'Trabajo';

  @override
  String get mobile => 'Móvil';

  @override
  String get draft => 'Borrador';

  @override
  String get main => 'Principal';

  @override
  String get oneHour => '1 hora';

  @override
  String nHours(int n) {
    final intl.NumberFormat nNumberFormat =
        intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return '$nString horas';
  }

  @override
  String get group => 'Grupo';

  @override
  String get ungroup => 'Desagrupar';

  @override
  String daysAgo(int n) {
    final intl.NumberFormat nNumberFormat =
        intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return '$nString days ago';
  }

  @override
  String hoursAgo(int n) {
    final intl.NumberFormat nNumberFormat =
        intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return '$nString hours ago';
  }

  @override
  String minutesAgo(int n) {
    final intl.NumberFormat nNumberFormat =
        intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return '$nString minutes ago';
  }

  @override
  String secondsAgo(int n) {
    final intl.NumberFormat nNumberFormat =
        intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return '$nString seconds ago';
  }

  @override
  String hoursLeft(int n) {
    final intl.NumberFormat nNumberFormat =
        intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return 'Quedan ${nString}h';
  }

  @override
  String minutesLeft(int n) {
    final intl.NumberFormat nNumberFormat =
        intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return 'Quedan ${nString}m';
  }

  @override
  String secondsLeft(int n) {
    final intl.NumberFormat nNumberFormat =
        intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return 'Quedan ${nString}s';
  }

  @override
  String get willStopSoon => 'Terminará pronto';

  @override
  String get permissionRequired => 'Permisos requeridos';

  @override
  String get trackingConsent =>
      'Bitacora.io recopila datos de ubicación para permitir el seguimiento de la ruta iniciada por el usuario incluso cuando la aplicación está cerrada o no está en uso.';

  @override
  String relatedEntries(int n) {
    final intl.NumberFormat nNumberFormat =
        intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return '$nString registros relacionados';
  }

  @override
  String get newReport => 'Nuevo Reporte';

  @override
  String get reportIncludeCaption =>
      'Elige los campos a incluir en tu reporte.';

  @override
  String get reportCustomizeCaption => 'Agrega un encabezado a tu reporte.';

  @override
  String get reportFileCouldNotBeFound =>
      'El archivo de reporte no fue encontrado.';

  @override
  String get noAppToOpen =>
      'No se encontró ninguna aplicación para abrir este archivo.';

  @override
  String get filters => 'Filtros';

  @override
  String get addFilters => 'Añadir Filtros';

  @override
  String get noActiveFilters => 'Sin filtros activos';

  @override
  String get reportFiltersCaption =>
      'Aplica filtros para selectionar registros.';

  @override
  String get externalSignee => '-Externo-';

  @override
  String get signatureType => 'Tipo de firma';

  @override
  String get search => 'Búsqueda';

  @override
  String get searchInput => 'Ingresa palabra o frase exacta a buscar...';

  @override
  String get noResults => 'Sin resultados';

  @override
  String get format => 'Formato';

  @override
  String get period => 'Período';

  @override
  String nDays(int n) {
    final intl.NumberFormat nNumberFormat =
        intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return '$nString días';
  }

  @override
  String distanceTraveled(String n, String unit) {
    return 'Distancia recorrida: $n $unit';
  }

  @override
  String averageSpeed(double n, String unit) {
    final intl.NumberFormat nNumberFormat =
        intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return 'Velocidad promedio: $nString $unit';
  }

  @override
  String get location => 'Ubicación';

  @override
  String get networkError => 'Error de conexión.';

  @override
  String get forgotPassword => '¿Olvidaste tu contraseña?';

  @override
  String get resetPassword => 'Restablecer contraseña';

  @override
  String get dontHaveAccount => '¿No tienes cuenta?';

  @override
  String get name => 'Nombre';

  @override
  String get phoneNumber => 'Teléfono';

  @override
  String get phoneNumbers => 'Teléfonos';

  @override
  String get organizations => 'Organizaciones';

  @override
  String get proPlan => 'Plan Pro';

  @override
  String get failedUserInvite => '⚠️ Error al enviar la invitación';

  @override
  String copyClipboard(String text) {
    return '$text copiado.';
  }

  @override
  String get activeOrganization => 'Organización activa';

  @override
  String get pdfOrExcel => 'PDF o Excel';

  @override
  String get createAndShareReport => 'Crear y compartir PDF';

  @override
  String get reportScreenTitle =>
      '¿Buscas crear reportes o exportar tus datos?';

  @override
  String get reportScreenMessage =>
      'Visita el panel web de Bitacora.io para administrar tu información.';

  @override
  String get reportTemplateBaseDescription => 'Regular';

  @override
  String get reportTemplateCompactDescription => 'Compacto';

  @override
  String get reportTemplateCompactMinimalDescription => 'Compacto Mínimo';

  @override
  String get reportTemplateCompactTwoColumnsDescription => 'Compacto 2 Col.';

  @override
  String get reportFailedToCreate => 'Error al crear reporte.';

  @override
  String syncDescription(String updatedAt) {
    return 'Actualizado $updatedAt';
  }

  @override
  String get loginRequired => 'Requiere iniciar sesion';

  @override
  String get updateRequired => 'Requiere actualizar aplicación';

  @override
  String get incompleteSync =>
      'Existen registros que no se han sincronizado. Asegúrate de tener buena conexión a Internet y toca en Sincronizar.';

  @override
  String get syncInProgress => 'Sincronización en proceso.';

  @override
  String get syncReport =>
      'Aún parece haber registros por sincronizarse en tu organización, tu reporte puede estar incompleto.';

  @override
  String get sessionExpired =>
      'La sesión ha expirado.\nIngresa tu contraseña para sincronizar tus datos.';

  @override
  String get apiExpired =>
      'Esta versión de la aplicación ya no es soportada.\nActualiza para sincronizar tus datos.';

  @override
  String get leaveForLater => 'Hacerlo más tarde';

  @override
  String get youWontBeAbleToSyncData => 'No podrás sincronizar datos';

  @override
  String get credentialNotMatchWithCurrentUser =>
      'Las credenciales no coinciden con el usuario actual.';

  @override
  String get userSettings => 'Preferencias de usuario';

  @override
  String get includeOnEntryCapture => 'Incluir en captura de registros';

  @override
  String get locationTrackingActive => 'Rastreo GPS activo';

  @override
  String locationTrackingFromAnotherOrg(String organizationName) {
    return 'El registro rastreado pertenece a $organizationName.';
  }

  @override
  String get myActiviy => 'Mi Actividad';

  @override
  String get myActiviyDescription =>
      'Visualiza la cantidad de registros creados esta semana y este mes';

  @override
  String get weekChartTitle => 'Esta semana';

  @override
  String get weekChartSubtitle => 'Comparada con la última semana';

  @override
  String get monthChartTitle => 'Este mes';

  @override
  String monthChartSubtitle(int count) {
    return 'registros comparados con $count del último mes';
  }

  @override
  String get allowTrackingUser => 'Rastrear mi ubicación';

  @override
  String get forThisOrganization => 'Para esta organization';

  @override
  String forOrganization(String organizationName) {
    return 'Para $organizationName';
  }

  @override
  String get backgroundLocationTracking =>
      'Rastreo GPS activo en segundo plano. Toca para ver.';

  @override
  String get systemPermissions => 'Permisos del sistema';

  @override
  String get phoneInputCaption =>
      'Para mejor soporte y próxima integración de WhatsApp.';

  @override
  String get privacyPolicy => 'Política de Privacidad';

  @override
  String get videoTutorials => 'Video Tutoriales';

  @override
  String get contactSupport => 'Contact Support';

  @override
  String get sendUsWhatsApp => 'Soporte por WhatsApp';

  @override
  String get emailSupport => 'Soporte por Correo';

  @override
  String supportMessage(String email) {
    return 'Hola, soy usuario de Bitacora.io $email, tengo una pregunta o comentario...';
  }

  @override
  String get loginSupportMessage => 'Hola, tengo una pregunta o comentario....';

  @override
  String get theme => 'Tema';

  @override
  String get system => 'Sistema';

  @override
  String get light => 'Claro';

  @override
  String get dark => 'Oscuro';

  @override
  String get allProjects => 'Todos: Proyectos / Categorías / Ubicaciones';

  @override
  String get menu => 'Menú';

  @override
  String get noScheduledTasks => 'No hay tareas programadas.';

  @override
  String get log => 'Actividad';

  @override
  String get inventory => 'Inventario';

  @override
  String get personnel => 'Personas';

  @override
  String get movement => 'Movimiento';

  @override
  String get outgoing => 'Salida';

  @override
  String get incoming => 'Entrante';

  @override
  String get invalidEmail => 'Email inválido';

  @override
  String get alreadyUsedEmail => 'Email duplicado para esta organización';

  @override
  String get invalidPhoneNumber => 'Teléfono inválido';

  @override
  String get emptyName => 'Nombre vacío';

  @override
  String get emptyPassword => 'Contraseña vacía';

  @override
  String get somethingWentWrong => 'Hubo un error inesperado.';

  @override
  String get tryAgainLater => 'Inténtalo más tarde.';

  @override
  String get requiredField => 'Este campo es requerido.';

  @override
  String get formQuantity => '# / Cant.';

  @override
  String get formTitle => 'Actividad / Título de Registro';

  @override
  String get formProject => 'Proy., Categoría o Ubicación';

  @override
  String get formSublocation => 'Sub-proy. cat. ubi.';

  @override
  String get formComments => 'Comentarios / Descripción';

  @override
  String get progressComments => 'Comentarios de progreso / Descripción';

  @override
  String get locationDenied => 'Los permisos de localización fueros denegados.';

  @override
  String get photoDenied => 'El acceso a las fotos fue denegado.';

  @override
  String get videoDenied => 'El acceso a videos fue denegado.';

  @override
  String get fileDenied => 'El acceso a almacenamiento fue denegado.';

  @override
  String get cameraDenied => 'El acceso a cámara fue denegado.';

  @override
  String get recordingDenied => 'Los permisos de grabación fueros denegados.';

  @override
  String get voiceRecognitionError =>
      '⚠️ Error debido al reconocimiento de voz.';

  @override
  String get goToAppSystemSettings => 'Ir a la configuración.';

  @override
  String get newEntries => 'Nuevos Registros';

  @override
  String get offered => 'Ofrecido';

  @override
  String get itemName => 'Nombre de Artículo';

  @override
  String get from_ => 'De:';

  @override
  String get to_ => 'A:';

  @override
  String get recipient => 'Receptor';

  @override
  String get provider => 'Proveedor';

  @override
  String get unit_ => 'PZA';

  @override
  String get total_ => 'TOT';

  @override
  String get expense => 'Gasto';

  @override
  String get income => 'Ingreso';

  @override
  String get reason => 'Razón';

  @override
  String get na => 'N/A';

  @override
  String get paid => 'PAGADO';

  @override
  String get unpaid => 'NO PAGADO';

  @override
  String get numHours_ => '# DE HORAS';

  @override
  String get time_ => 'Hora';

  @override
  String get hours => 'Horas';

  @override
  String get entrance => 'Entrada';

  @override
  String get exit => 'Salida';

  @override
  String get scheduleEntry => 'Programar registro';

  @override
  String get startDate => 'Fecha inicial';

  @override
  String get endDate => 'Fecha final';

  @override
  String get startTime => 'Hora inicial';

  @override
  String get endTime => 'Hora final';

  @override
  String get timer => 'Cronómetro';

  @override
  String get entryTimer => 'Cronómetro';

  @override
  String get moreThanOneTimer => 'No puedes tener más de un cronómetro activo.';

  @override
  String get moreThanOneLocationTracking =>
      'No puedes tener más de un seguimiento GPS activo.';

  @override
  String get progressive => 'Progresivo';

  @override
  String get complete => 'Completo';

  @override
  String get myFirstReportIsReady => '¡Tu primer reporte PDF esta listo!';

  @override
  String get scheduledEntry => 'Registro programado';

  @override
  String get incomeOrExpense => 'Ingresos y/o gastos';

  @override
  String get addAComment => 'Añadir un comentario';

  @override
  String get assignedTo => 'Asignado a';

  @override
  String get assignedToMe => 'Asignado a mí';

  @override
  String get moreFields => 'Más campos';

  @override
  String get logoutTitle => 'Cerrar sesión';

  @override
  String get logoutMessage => '¿Estás seguro de que deseas cerrar tu sesión?';

  @override
  String get logoutWillLoseData =>
      '*Tienes cambios locales, se perderán permanentemente.';

  @override
  String get chatWillBeLost => 'El chat se perderá al salir.';

  @override
  String get writeSomething => 'Escribe algo...';

  @override
  String get entryNotSaved => 'Registro sin guardar';

  @override
  String get doodleNotSaved => 'Doodle sin guardar';

  @override
  String get willLoseData => '¿Qué deseas hacer con los cambios?';

  @override
  String get chooseYourOrg => 'Selecciona tu organización activa';

  @override
  String get wereInvitedOrg => 'Fuiste invitado a una nueva organización';

  @override
  String get switchOrganization => 'Cambia de organización';

  @override
  String get reportEntryType => 'Tipo de registros';

  @override
  String get photoPdf => 'Foto PDF';

  @override
  String get startDateEndDate => 'Inicio / Final';

  @override
  String get incomeExpense => 'Ingreso / Gasto';

  @override
  String get providerStatus => 'Proveedor / Estatus';

  @override
  String get startTomorrow => 'Iniciar mañana';

  @override
  String get finishToday => 'Terminar hoy';

  @override
  String get finishTomorrow => 'Terminar mañana';

  @override
  String get scheduledEntries => 'Tareas programadas';

  @override
  String get notificationTime => 'Hora de notificación';

  @override
  String get notifyDayOf => 'Notificar el mismo día';

  @override
  String get alertOnTheStartAndOrEndDayOfTheTask =>
      'Alerta el día que inicia y termina la tarea';

  @override
  String get notifyTheDayBefore => 'Notificar el día anterior';

  @override
  String get alertTheDayBeforeTheTaskStartsAndOrEnds =>
      'Alerta un día antes de iniciar y terminar de la tarea';

  @override
  String get dailyReminder => 'Recordatorio diario';

  @override
  String get dailyReminderDescription =>
      'Notifica cada día recordándote ingresar tus notas y evidencia';

  @override
  String get logYourActivitiesOfToday => 'Registra tus actividades del día';

  @override
  String get dailyReminderBody =>
      'No olvides ingresar tus notas y evidencias del día para que no se nos acumule el trabajo.';

  @override
  String get usePreviousVersion => 'Usar versión previa';

  @override
  String get bugReport => 'Informe de error';

  @override
  String get bugReportChannelDescription =>
      'Notifica cuando tu informe de error está preparado y te permite compartirlo.';

  @override
  String get bugReportTapToShare => 'Toca para compartir.';

  @override
  String get bugReportHint => 'Por favor describe tu problema.';

  @override
  String get deleteAccountTitle => 'Eliminar cuenta';

  @override
  String get deleteAccountMessage =>
      'Se cerrará la sesión e iniciará el proceso de eliminación de tu cuenta.';

  @override
  String get farFromQr => 'Estás muy lejos de la ubicación del código QR.';

  @override
  String get qrDoesNotBelongOrg =>
      'Este código QR no pertenece a la organización activa';

  @override
  String get deleteTitle => '¿Eliminar registro?';

  @override
  String get deleteMessage => 'El registro se eliminará de forma permanente.';

  @override
  String get deleteSelectionTitle => '¿Eliminar registros seleccionados?';

  @override
  String get deleteSelectionMessage =>
      'Los registros se eliminarán de forma permanente.';

  @override
  String get ungroupGroupTitle => '¿Deshacer grupo?';

  @override
  String get ungroupGroupMessage =>
      'El grupo se eliminará de forma permanente.';

  @override
  String selectedEntries(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$countString registros seleccionados',
      one: '$countString registro seleccionado',
    );
    return '$_temp0';
  }

  @override
  String selectedNames(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count nombres seleccionados',
      one: '$count nombre seleccionado',
    );
    return '$_temp0';
  }

  @override
  String get deleteReport =>
      'Los reportes seleccionados se eliminarán de forma permanente.';

  @override
  String get freePlan => 'Plan Gratuito';

  @override
  String get freePlanDescription =>
      'Registra tus notas de actividad día a día y visualízalo en web y app.';

  @override
  String get trialPlan => 'Plan de Prueba';

  @override
  String get trialPlanDescription =>
      'Captura, crea reportes y suma colaboradores para explorar en web y app.';

  @override
  String get discoverBitacora => 'Empieza a descubrir Bitacora.io:';

  @override
  String get syncPhotosAndFiles => 'Sincroniza fotos y archivos';

  @override
  String get syncPhotosAndFilesDescription =>
      'Obtén archivos en todos los dispositivos, disponibles en la web y reportes.';

  @override
  String get pdfReports => 'Reportes PDF';

  @override
  String get pdfReportsDescription =>
      'Genera reportes automatizados a partir de tus registros.';

  @override
  String get entriesNotYetSyncToastError => 'Registros aún no sincronizados.';

  @override
  String get pasteErrorToast =>
      'Error al pegar desde el portapapeles. Intenta de nuevo.';

  @override
  String get powerTools => 'Herramientas adicionales';

  @override
  String get powerToolsDescription =>
      'Códigos QR, firmas, gráficos, rastreo GPS, tableros, alertas personalizadas y más.';

  @override
  String get onboarding1Title => 'Registros organizados, reportes automáticos.';

  @override
  String get onboarding2Title => 'Registra todo.';

  @override
  String get onboarding2Subtitle =>
      'Captura notas, actividades, trabajos y evidencias - organizado para consultar después facilmente.';

  @override
  String get onboarding3Title => 'Crea reportes automáticos.';

  @override
  String get onboarding3Subtitle =>
      'Crea y comparte reportes en PDF o Excel para compartir con colaboradores y clientes - tu defines que concluir.';

  @override
  String get onboarding4Title =>
      'Profesionaliza tu trabajo y reduce tiempos con herramientas poderosas.';

  @override
  String get knowAllFeatures => 'Conoce Bitacora.io';

  @override
  String get noLongerAbleToEdit => 'Ya no es posible editar el registro.';

  @override
  String get noPermissionToEdit => 'No tienes permiso para editar el registro.';

  @override
  String get wrongDateForEntry =>
      'No es posible guardar el registro en la fecha seleccionada.';

  @override
  String get aiAssistant => 'Asistente Inteligente';

  @override
  String get experimentalModeEnabled => 'Modo experimental activado';

  @override
  String get experimentalModeDisabled => 'Modo experimental desactivado';

  @override
  String get transcript => 'Transcripción:';

  @override
  String get sentiment => 'Sentimiento:';

  @override
  String get keywords => 'Palabras clave:';

  @override
  String get actionItems => 'Elementos de acción:';

  @override
  String get generatedWithAiTitle => '✨ Generando con IA...';

  @override
  String get generatedErrorWithAiTitle =>
      '⚠️ No pudimos generar tu registro con IA.';

  @override
  String get generatedWithAiDescription =>
      'Estamos creando este registro con IA, un momento por favor.';

  @override
  String get generatedErrorWithAiDescription =>
      'Tus archivos multimedia se guardaron como attachments en este registro.';

  @override
  String get aiGeneratedNotification =>
      'This entry was created from an audio recording, photos or video.';

  @override
  String get aiResourceNotSelected =>
      'No resource was selected to generate an entry';

  @override
  String get aiSettings => 'Configuración de IA';

  @override
  String get aiInstructions =>
      'Instrucciones adicionales para la generación con IA';

  @override
  String get aiInstructionsHint =>
      'Ej: Sé más detallado, usa palabras técnicas...';

  @override
  String aiCreditsLimitReached(String feature) {
    return 'Has alcanzado el límite diario de generaciones AI gratuitas para $feature. Los créditos se renovarán mañana.';
  }

  @override
  String aiAssistantGreeting(String userName) {
    return 'Hola, $userName';
  }

  @override
  String aiAssistantHeaderMessage(String userName) {
    return 'Hola $userName, estoy aquí para ayudarte a crear más registros';
  }

  @override
  String aiAssistantGreeting1(String userName) {
    return 'Hola $userName, ¿qué quieres documentar hoy? Comparte tu información abajo.';
  }

  @override
  String aiAssistantGreeting2(String userName) {
    return '¡Hola $userName! Estoy lista para ayudarte a crear registros. Comparte tu información abajo.';
  }

  @override
  String aiAssistantGreeting3(String userName) {
    return 'Hola $userName, lista cuando tu estés. Comparte tu información abajo.';
  }

  @override
  String aiAssistantGreeting4(String userName) {
    return '¡Hola $userName! ¿Cómo estuvo tu día hoy? Compárteme tu actividad abajo.';
  }

  @override
  String aiAssistantGreeting5(String userName) {
    return 'Hola $userName, estoy aquí para convertir tu información en reportes, solo presiona abajo';
  }

  @override
  String get aiAssistantFileSelected1 =>
      'Perfecto, he recibido tu archivo. ¿Quieres agregar más contenido?';

  @override
  String get aiAssistantFileSelected2 =>
      'Excelente elección. ¿Deseas incluir más archivos antes de generar?';

  @override
  String get aiAssistantFileSelected3 =>
      'Archivo recibido. Puedes agregar más o proceder a generar tu registro';

  @override
  String get aiAssistantFileSelected4 =>
      '¡Genial! ¿Hay algo más que quieras incluir en este registro?';

  @override
  String get aiAssistantFileSelected5 =>
      'Archivo listo. ¿Agregamos más contenido o generamos el registro?';

  @override
  String get aiAssistantDefaultUser => 'Usuario';

  @override
  String get aiAssistantFiles => 'Archivos';

  @override
  String get aiAssistantGallery => 'Galería';

  @override
  String get aiAssistantCamera => 'Cámara';

  @override
  String get aiAssistantRecordVideo => 'Grabar Video';

  @override
  String get aiAssistantAudio => 'Audio';

  @override
  String get aiAssistantGenerate => 'Generar';

  @override
  String get aiAssistantGenerating1 =>
      'Estoy analizando tu contenido y creando el registro';

  @override
  String get aiAssistantGenerating2 =>
      'Procesando tu información para generar un registro detallado';

  @override
  String get aiAssistantGenerating3 =>
      'Trabajando en tu registro, esto tomará solo un momento';

  @override
  String get aiAssistantGenerating4 =>
      'Creando tu registro con toda la información proporcionada';

  @override
  String get aiAssistantGenerating5 =>
      'Analizando y organizando tu contenido en un registro profesional';

  @override
  String get aiAssistantGenerating => 'Estoy generando su registro';

  @override
  String get aiAssistantCompleted1 =>
      '¡Listo! He generado tu registro. ¿Te gustaría crear otro?';

  @override
  String get aiAssistantCompleted2 =>
      'Tu registro ha sido creado exitosamente. ¿Qué más quieres documentar hoy?';

  @override
  String get aiAssistantCompleted3 =>
      '¡Perfecto! He terminado de procesar tu registro. ¿Tienes más contenido para convertir?';

  @override
  String get aiAssistantCompleted4 =>
      'He completado tu registro. ¿Qué te parece el resultado? ¿Creamos otro?';

  @override
  String get aiAssistantCompleted5 =>
      '¡Excelente! Tu registro está listo. ¿Hay algo más que quieras documentar?';

  @override
  String get aiAssistantCompleted => 'Aquí está su registro generado';

  @override
  String get aiAssistantError =>
      'Hubo un problema generando su registro. Por favor, inténtelo de nuevo.';

  @override
  String get aiAssistantInvitation1 => '¿Te gustaría crear otro registro?';

  @override
  String get aiAssistantInvitation2 => '¿Qué más quieres documentar hoy?';

  @override
  String get aiAssistantInvitation3 =>
      'Puedes agregar más archivos para crear otro registro';

  @override
  String get aiAssistantInvitation4 =>
      '¿Tienes más contenido que quieras convertir en un registro?';

  @override
  String get aiAssistantInvitation5 =>
      'Estoy aquí para ayudarte a crear más registros';
}
