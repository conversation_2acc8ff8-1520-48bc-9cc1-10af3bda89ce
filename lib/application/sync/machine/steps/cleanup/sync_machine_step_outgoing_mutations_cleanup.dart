import 'package:bitacora/application/sync/machine/sync_machine_step.dart';
import 'package:bitacora/util/logger/logger.dart';

class SyncMachineStepOutgoingMutationsCleanup extends SyncMachineStep {
  static const int _maxFailedAttemptsThreshold = 100;

  SyncMachineStepOutgoingMutationsCleanup(super.params);

  @override
  Future<void> performSync() async {
    logger.i('sync:cleanup Starting outgoing mutations cleanup');
    
    final context = db.context();
    final deletedCount = await _deleteStuckOutgoingMutations(context);
    
    if (deletedCount > 0) {
      logger.i('sync:cleanup Deleted $deletedCount stuck outgoing mutations');
    } else {
      logger.i('sync:cleanup No stuck outgoing mutations found');
    }
  }

  Future<int> _deleteStuckOutgoingMutations(context) async {
    final executor = await context.executor;

    return executor.delete(
      'outgoingMutation',
      where: 'om_failedAttempts >= ?',
      whereArgs: [_maxFailedAttemptsThreshold],
    );
  }

  @override
  String get debugName => 'outgoing-mutations-cleanup';
}
