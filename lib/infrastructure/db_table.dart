import 'dart:async';

import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/common/value_object/value_object.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_query_scope_utils.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:flutter/widgets.dart';
import 'package:sqflite/sqflite.dart';

typedef IdResolver<T extends Model> = Future<LocalId?> Function(
    DbContext context, T model);

abstract class DbTable<T extends Model, F>
    implements RepositoryTable<T, DbContext, F> {
  final StreamController<Mutation<T>> _mutations =
      StreamController<Mutation<T>>.broadcast();
  final List<IdResolver<T>> _idResolvers = <IdResolver<T>>[];
  final bool isSyncable;

  DbTable({this.isSyncable = true}) {
    addIdResolver(_idResolverById);
    if (isSyncable) {
      addIdResolver(_idResolverByRemoteId);
    }
  }

  DbContract get contract => throw UnimplementedError(); // FIXME: make abstract

  String get tableName => contract.tableName;

  String get idColumn => '${contract.prefix}id';

  String get remoteIdColumn {
    if (!isSyncable) {
      throw UnsupportedError('$this is not syncable.');
    }
    return '${contract.prefix}remoteId';
  }

  List<String> get searchColumns => [];

  DbTranslator<T> get translator;

  @override
  Future<LocalId?> save(
    DbContext context,
    T model, {
    bool requestSync = false,
  }) async {
    final cache = context.db.cache.table<T>();
    final cachedId = cache?.cachedIdWithFieldChecks(context.db.cache, model);
    if (cachedId != null) {
      return cachedId;
    }

    final mutation =
        await wrapTransaction<Mutation<T>?>(context, (wrappedContext) async {
      final saved = await _doSave(wrappedContext, model);
      if (saved == null) {
        /// Model could be empty except for id. In which case, we want to
        /// respect intent to requestSync regardless of actual db mutation.
        /// FIXME: maybe model is no longer in db?
        if (requestSync && model.id != null) {
          await onSyncRequested(
            wrappedContext,
            Mutation(type: MutationType.update, id: model.id),
          );
        }
        return null;
      }

      cache?.save(context.db.cache, saved.id!, model);
      await onSaved(wrappedContext, saved);
      if (requestSync) {
        await onSyncRequested(wrappedContext, saved);
      }
      return saved;
    });
    if (mutation != null) {
      broadcastMutation(mutation);
    }
    return mutation != null ? mutation.id : model.id;
  }

  Future<Mutation<T>?> _doSave(DbContext context, T model) async {
    final values = await translator.toDb(context, model);
    if (values.isEmpty || (values.length == 1 && values[idColumn] != null)) {
      return null;
    }
    final executor = await context.executor;

    final id = await _resolveIdPreSave(context, model);

    logger.t('db:save ${id?.displayValue} from $tableName values $values');

    if (id != null) {
      await executor.update(tableName, values,
          where: '$idColumn = ?', whereArgs: [id.dbValue]);
      return Mutation<T>(
        type: MutationType.update,
        id: id,
        model: model,
      );
    }

    int insert;
    try {
      insert = await executor.insert(tableName, values);
    } on DatabaseException catch (e, s) {
      logger.f('db:save Failed to insert $e \n$s');
      fixFailedInsertValues(values, e);
      insert = await executor.insert(tableName, values);
      logger.i('db:save Insert hack succeeded');
    }
    return Mutation<T>(
      type: MutationType.insert,
      id: LocalId(insert),
      model: model,
    );
  }

  @protected
  void addIdResolver(IdResolver<T> idResolver) {
    _idResolvers.add(idResolver);
  }

  Future<LocalId?> _resolveIdPreSave(DbContext context, T model) async {
    for (final idResolver in _idResolvers) {
      final id = await idResolver(context, model);
      if (id != null) {
        return id;
      }
    }
    return null;
  }

  Future<LocalId?> _idResolverById(DbContext context, T model) async {
    if (model.id != null) {
      return model.id;
    }
    return null;
  }

  Future<LocalId?> _idResolverByRemoteId(DbContext context, T model) async {
    if (model.remoteId?.value == null) {
      return null;
    }
    final result = await (await context.executor).query(
      tableName,
      columns: [idColumn],
      where: '$remoteIdColumn = ?',
      whereArgs: [model.remoteId!.dbValue],
      limit: 1,
    );
    return result.isEmpty ? null : LocalId(result[0][idColumn] as int);
  }

  @protected
  Future<void> onSaved(DbContext context, Mutation<T> mutation) async {}

  /// If we fail to insert an entry, tables get a chance to fix the values
  /// and try again.
  @protected
  void fixFailedInsertValues(Map<String, dynamic> values, DatabaseException e) {
    throw e;
  }

  @protected
  Future<void> onSyncRequested(DbContext context, Mutation<T> mutation) async {}

  @override
  Future<int> delete(
    DbContext context,
    LocalId id, {
    bool requestSync = false,
  }) async {
    logger.t('db:delete ${id.dbValue} from $tableName');

    final mutation = Mutation<T>(
      type: MutationType.delete,
      id: id,
    );
    final rowsDeleted = await wrapTransaction(context, (wrappedContext) async {
      if (requestSync) {
        await onSyncRequested(wrappedContext, mutation);
      }
      await onPreDelete(wrappedContext, id);
      final deleted = _doDelete(wrappedContext, id);
      await onPostDelete(wrappedContext, id);
      return deleted;
    });

    if (rowsDeleted > 0) {
      broadcastMutation(mutation);
    }
    return rowsDeleted;
  }

  Future<int> _doDelete(DbContext context, LocalId id) async {
    context.db.cache.table<T>()?.clear();

    return (await context.executor).delete(
      tableName,
      where: '$idColumn = ?',
      whereArgs: [id.dbValue],
    );
  }

  @protected
  Future<void> onPreDelete(DbContext context, LocalId id) async {}

  @protected
  Future<void> onPostDelete(DbContext context, LocalId id) async {}

  @override
  Future<T?> find(DbContext context, LocalId id) async {
    return _findByIdColumn(context, idColumn, id);
  }

  @override
  Future<T?> findByRemoteId(DbContext context, RemoteId remoteId) async {
    return _findByIdColumn(context, remoteIdColumn, remoteId);
  }

  Future<T?> _findByIdColumn(
      DbContext context, String column, ValueObject id) async {
    final cache = context.db.cache.table<T>();
    final cached =
        cache?.cachedModelWithFieldChecks(context.db.cache, id, context);
    if (cached != null) {
      return cached;
    }

    return single(query(
      context,
      where: '$column = ?',
      whereArgs: [id.dbValue],
      limit: 2,
    ));
  }

  @override
  Future<T?> search(DbContext context, String pattern) async {
    final columns = searchColumns;
    if (columns.isEmpty) {
      return null;
    }
    // FIXME: implement for multiple columns
    return single(query(
      context,
      where:
          '${columns[0]} = ? AND ${DbQueryScopeUtils().where(context.queryScope!)}',
      whereArgs: [
        pattern,
        ...DbQueryScopeUtils().queryArg(context.queryScope!)
      ],
      limit: 1,
    ));
  }

  Future<T?> single(Future<List<T>> futureList) async {
    final list = await futureList;

    assert(list.length <= 1);

    return list.isEmpty ? null : list[0];
  }

  Future<T?> takeFirst(Future<List<T>> futureList) async {
    final list = await futureList;
    return list.isEmpty ? null : list[0];
  }

  Future<List<T>> query(
    DbContext context, {
    bool? distinct,
    String? where,
    List<Object>? whereArgs,
    String? groupBy,
    String? having,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    query(DbContext c) async {
      final list = await (await c.executor).query(
        tableName,
        distinct: distinct,
        columns: columnsFromFields(c.fields!.map.values),
        where: where,
        whereArgs: whereArgs,
        groupBy: groupBy,
        having: having,
        orderBy: orderBy,
        limit: limit,
        offset: offset,
      );
      return Future.wait(list.map((e) => fromDb(c, e)));
    }

    if (context.hasAnyOfFields(translator.nestedModelFields)) {
      return wrapTransaction(context, (c) => query(c));
    }
    return query(context);
  }

  Future<List<T>> rawQuery(
    DbContext context,
    String sqlQuery,
    List<Object>? arguments,
  ) async {
    query(DbContext c) async {
      final list = await (await c.executor).rawQuery(sqlQuery, arguments);
      return Future.wait(list.map((e) => fromDb(c, e)));
    }

    if (context.hasAnyOfFields(translator.nestedModelFields)) {
      return wrapTransaction(context, (c) => query(c));
    }
    return query(context);
  }

  @override
  Future<int> count(DbContext context) async {
    final executor = await context.executor;
    final list = await executor.query(tableName, columns: [idColumn]);
    return list.length;
  }

  @override
  Stream<Mutation<T>> getMutations() {
    return _mutations.stream;
  }

  @override
  void markDirty() {
    broadcastMutation(Mutation(type: MutationType.unknown));
  }

  @protected
  void broadcastMutation(Mutation<T> mutation) {
    _mutations.sink.add(mutation);
  }

  Future<T> fromDb(DbContext context, Map<String, dynamic> map) async {
    final result = await translator.fromDb(context, map);
    final cache = context.db.cache.table<T>();
    cache?.save(context.db.cache, result.id!, result);
    return result;
  }
}

Future<T> wrapTransaction<T>(DbContext context,
    Future<T> Function(DbContext wrappedContext) query) async {
  // FIXME: move somewhere more central,
  if (context.txn != null) {
    return query(context);
  }
  return context.db.transaction((c) {
    return query(context.copyWith(txn: c.txn!));
  });
}
